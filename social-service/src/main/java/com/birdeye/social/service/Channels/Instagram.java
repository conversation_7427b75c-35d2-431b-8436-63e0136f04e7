package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dto.PicturesqueMediaCallback;
import com.birdeye.social.dto.PostInsightDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.PageInsightV2EsData;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.InstagramAccountDataResponse;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetails;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetailsDTO;
import com.birdeye.social.model.competitorProfile.CompetitorProfileReportESData;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.service.InstagramSocialService;
import com.birdeye.social.service.SocialCompetitorService.Instagram.InstagramCompetitorService;
import com.birdeye.social.service.SocialCompetitorService.SocialCompetitor;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialReportService.Instagram.InstagramInsights;
import com.birdeye.social.service.SocialReportService.Instagram.InstagramReportService;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.service.notification.NotificationAuditService;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class Instagram implements SocialInsights, NotificationAuditService, SocialCompetitor, PostOperation {

    @Autowired
    private InstagramInsights instagramInsights;

    @Autowired
    private InstagramReportService instagramReportService;

    @Autowired
    private InstagramSocialService instagramSocialService;

    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;

    @Autowired
    private InstagramCompetitorService instagramCompetitorService;

    private static final Logger log = LoggerFactory.getLogger(Instagram.class);

    @Override
    public String channelName() {
        return SocialChannel.INSTAGRAM.getName();
    }

    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) throws Exception {
        //not supported for ig
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        //not supported for ig
    }

    @Override
    public List<SocialNotificationAudit> putNotificationForChannel(Object notificationObject) {
        return instagramSocialService.auditNotification(notificationObject);
    }

    @Override
    public Object getPageInsightsFromES(InsightsRequest insights) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}",insights.getEnterpriseId());
        if(Objects.isNull(insights)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insights.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.nonNull(searchTemplate)) {
            return instagramInsights.getInstagramInsightsForPage(insights);
        }
        return null;
    }

    @Override
    public Object getPageInsightsESData(InsightsRequest insights) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}",insights.getEnterpriseId());
        if(Objects.isNull(insights)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insights.getReportType());
        log.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.equals(searchTemplate)){
            PageInsightV2EsData engageData = instagramInsights.getInstagramInsightsForMessageSent(insights);

            PageInsightV2EsData postData = instagramInsights.getInstagramInsightsForPublishPost(insights);
            if(Objects.nonNull(postData)) {
                engageData.setBuckets(postData.getBuckets());
                engageData.setCurrentData(postData.getCurrentData());
            }
            insights.setReportType(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.getName());
            return engageData;
        } else if (SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC.equals(searchTemplate)) {
            return instagramInsights.getInstagramInsightsForPublishPost(insights);
        } else {
            return instagramInsights.getPageInsightsESData(insights);
        }
    }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {
        if(Objects.isNull(pageInsights)){
            return;
        }
        instagramInsights.postInstagramPageInsightToES(pageInsights);
    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {
        if(CollectionUtils.isEmpty(businessPosts)){
            return;
        }
        instagramInsights.getPostInsights(businessPosts.get(0),isFreshRequest);
    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO) {
        instagramReportService.getPageInsightsFromInstagram(socialScanEventDTO);
    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {
        instagramInsights.postInstagramPostInsightsToEs(postData);
    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return instagramInsights.getInstagramInsightsForPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) {

    }

    public void updatePageInsightsPostCount(PageInsights pageInsights) {
        if(Objects.isNull(pageInsights)) {
            return;
        }
        instagramInsights.updateInstagramPageInsightsPostCount(pageInsights);
    }

    @Override
    public String getPageInsightIndexName() {
        return ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName();
    }

    @Override
    public void startScanForPosts(String pageId) {
        instagramInsights.startScanForPosts(pageId);
    }

    @Override
    public void updatePageInsightsDb( String pageId, Integer businessId, Long enterpriseId) {

    }

	@Override
	public void getGMBPageAnalytics(String pageId, Integer businessId) {
		// TODO Auto-generated method stub

	}
    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {
        if(Objects.isNull(businessPosts)) {
            return;
        }
        instagramInsights.saveCDNPostToES(businessPosts);
    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {
        // TODO Auto-generated method stub

    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest) {
       return instagramInsights.getInstagramPerformanceData(insightsRequest);
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return instagramInsights.backfillProfilePagesToEs(pageInsights);
    }
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.REPORT_MESSAGE_VOLUME.equals(searchTemplate)){
            return instagramInsights.getMessageVolumeInsightsReportData(insightsRequest);
        } else {
            return instagramInsights.getInstagramInsightsReportData(insightsRequest);
        }

    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        List<BusinessInstagramAccount> instagramAccounts = instagramAccountRepository.findAllByBusinessIdIn(businessIds);
        if(CollectionUtils.isNotEmpty(instagramAccounts)){
            Map<String, Integer> businessIdPageIdMap = new HashMap<>();
            instagramAccounts.forEach(account -> {
                businessIdPageIdMap.put(account.getInstagramAccountId(), account.getBusinessId());
            });
            return businessIdPageIdMap;
        }
        return null;
    }

    @Override
    public void fetchCompetitorPosts(CompetitorRequestDTO competitorRequestDTO) {
        if(Objects.nonNull(competitorRequestDTO.getUserName())) {
            instagramCompetitorService.fetchCompetitorPosts(competitorRequestDTO);
        } else {
            log.info("[IG competitor posts] username cannot be null in request, exiting the flow!!");
        }
    }
    @Override
    public void fetchCompetitorAccounts(List<String> accountIdentifier, Long enterpriseId) {
        if(CollectionUtils.isNotEmpty(accountIdentifier)) {
            instagramCompetitorService.fetchCompetitorAccounts(accountIdentifier, enterpriseId);
        } else {
            log.info("Empty list received in input");
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, ErrorCodes.INVALID_REQUEST.name());
        }
    }

    @Override
    public void updateCompCache(SocialChannel channel, Long businessNumber) {
        instagramCompetitorService.updateCompCache(channel, businessNumber);
    }

    @Override
    public CompetitorListResponse getCompetitorList(Long businessNumber) {
        return instagramCompetitorService.getCompetitorList(businessNumber);
    }

    @Override
    public void deleteCompetitor(DeleteCompetitorDetailRequest deleteRequest, Long enterpriseId) throws SocialBirdeyeException {
        instagramCompetitorService.deleteCompetitor(deleteRequest, enterpriseId);
    }

    @Override
    public String getPageIdOnCompId(Integer rawCompId) {
        return instagramCompetitorService.getPageIdOnCompId(rawCompId);
    }

    @Override
    public void scanPages() {
        instagramCompetitorService.scanPages();
    }

    @Override
    public List<CompetitorBasicDetail> getCompetitorsBasicDetails(List<String> pageIds) {
        return instagramCompetitorService.getCompetitorsBasicDetails(pageIds);
    }

    @Override
    public void updateProfilePictureUrl(PicturesqueMediaCallback picturesqueMediaCallback, Integer rawCompId) {
        instagramCompetitorService.updateProfilePictureUrl(picturesqueMediaCallback, rawCompId);
    }


    @Override
    public void callPicturesQueForPage(PicturesqueCompRequest request) {
        instagramCompetitorService.callPicturesQueForPage(request);
    }

    @Override
    public CompetitorPageDetailResponse getPageSummary(String userName, Long businessNumber) {
        return instagramCompetitorService.getPageSummary(userName, businessNumber);
    }

    @Override
    public void proceedToUnmapPage(Long businessNumber) {
        instagramCompetitorService.proceedToUnmapPage(businessNumber);
    }

    @Override
    public Optional<Integer> fetchCompetitorProfileFollowerCount(CompetitorRequestDTO competitorRequestDTO) throws SocialBirdeyeException {
        return instagramCompetitorService.fetchCompetitorProfileFollowerCount(competitorRequestDTO);
    }

    @Override
    public CompetitorProfileReportESData getCompetitorProfileDataES(SearchRequest searchRequest, String channel) {
        return null;
    }

    @Override
    public Map<String, CompetitorPageDetails> getPageIdVsPageName(List<String> pageIds) throws SocialBirdeyeException {
        return instagramCompetitorService.getPageNameByPageId(pageIds);
    }

    @Override
    public Map<String, CompetitorPageDetails> getLocationIdVsPageId(List<String> pageIds) throws SocialBirdeyeException {
        Map<String, CompetitorPageDetails> response = new HashMap<>();
        try {
            List<BusinessInstagramAccount> businessInstagramAccountList = instagramAccountRepository.findByInstagramAccountIdIn(pageIds);
            if (CollectionUtils.isNotEmpty(businessInstagramAccountList)) {
                businessInstagramAccountList.forEach(account -> response.put(account.getInstagramAccountId(),
                        CompetitorPageDetails.builder()
                                .profilePictureUrl(account.getInstagramAccountPictureUrl())
                                .pageName(StringUtils.isEmpty(account.getInstagramAccountName())
                                    ? account.getInstagramHandle() : account.getInstagramAccountName())
                                .pageLink(account.getInstagramLink())
                                .channel(SocialChannel.INSTAGRAM.getName())
                                .pageId(account.getInstagramAccountId())
                                .build()));
            }
        }catch (Exception e){
            log.error("Error while fetching locationId vs pageId mapping {}",e.getMessage());
        }
        return response;
    }

    @Override
    public List<CompetitorPageDetailsDTO> getPageDetails(List<String> pageIds) {
      return instagramCompetitorService.getPageDetails(pageIds);
    }

    @Override
    public List<CompetitorPageDetailsDTO> getSelfPageDetails(List<String> pageIds) {
        List<CompetitorPageDetailsDTO> response = new ArrayList<>();


        List<BusinessInstagramAccount> instagramAccounts = instagramAccountRepository.findByInstagramAccountIdIn(pageIds);
        if(CollectionUtils.isEmpty(instagramAccounts)) {
            return new ArrayList<>();
        }

        for(BusinessInstagramAccount instagramAccount: instagramAccounts) {
            CompetitorPageDetailsDTO data =  CompetitorPageDetailsDTO.builder()
                    .pageName(instagramAccount.getInstagramAccountName())
                    .pageId(instagramAccount.getInstagramAccountId())
                    .pageProfileUrl(instagramAccount.getInstagramAccountPictureUrl())
                    .build();
            response.add(data);
        }
        return response;
    }

    @Override
    public Integer getCompetitorAccounts(Long enterpriseId) throws SocialBirdeyeException {
        return instagramCompetitorService.getCompetitorAccounts(enterpriseId);
    }

    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return instagramInsights.createPostData(businessPosts, new PostInsightDTO(), new InstagramAccountDataResponse());
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {
        instagramInsights.backfillEngagmentBreakDown(backfillInsightReq);
    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {
        instagramReportService.backfillEngagmentData(backfillRequest);
    }

    @Override
    public List<String> getPageIds(List<Integer> businessIds) {
        List<String> pageIds =  instagramAccountRepository.findDistinctInstagramIdByBusinessIdIn(businessIds);
        if(CollectionUtils.isEmpty(pageIds)){
            return new ArrayList<>();
        }
        return pageIds;
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria,SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        return instagramInsights.getPostLeadershipReport(insightsRequest,postSortingCriteria,order,startIndex,pageSize);
    }

    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return instagramAccountRepository.findDistinctInstagramAccountIdByBusinessIdIn(businessIds);
    }

    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return instagramInsights.getInstagramInsightsForTopPost(insightsRequest,startIndex,pageSize,sortParam,sortOrder, excelDownload);
    }

    @Override
    public void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds,List<LocationPagePair> locationPagePairList,
                                                      Map<String, Boolean> pagePermissionMap) {
        List<BusinessInstagramAccount> instagramAccounts = instagramAccountRepository.findAllByBusinessIdIn(businessIds);
        if (CollectionUtils.isNotEmpty(instagramAccounts)) {
            instagramAccounts.forEach(account -> {
                updateLocationPagePairAndPagePermissionMap(locationPagePairList,
                        pagePermissionMap, account.getInstagramAccountId(), account.getBusinessId(), account.getCanPost());
            });
        }
    }

    @Override
    public void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList,
                                                   Map<String, Boolean> pagePermissionMap) {
        List<BusinessInstagramAccount> instagramAccounts = instagramAccountRepository.findByInstagramAccountIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(instagramAccounts)) {
            instagramAccounts.forEach(account -> {
                updateLocationPagePairAndPagePermissionMap(locationPagePairList,
                        pagePermissionMap, account.getInstagramAccountId(), account.getBusinessId(), account.getCanPost());
            });
        }
    }

    private void updateLocationPagePairAndPagePermissionMap(List<LocationPagePair> locations, Map<String, Boolean> pagePermissionMap,
                                                            String pageId, Integer businessId, Integer canPost) {
        locations.add(new LocationPagePair(pageId, businessId));
        if (pagePermissionMap != null) {
            pagePermissionMap.put(pageId, Objects.equals(canPost,1));
        }
    }


    @Override
    public List<String> getAllPageIds(int page, int size, Long enterpriseId) {
        return Objects.nonNull(enterpriseId)? instagramAccountRepository.findDistinctInstagramAccountIdByEnterpriseId(enterpriseId):
                instagramAccountRepository.findPageIdsWithPagination(new PageRequest(page,size));
    }

    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {

    }

    @Override
    public Map<String, PageInfoDto> getPageInfoDto(List<String> pageIds) {
        return Collections.emptyMap();
    }
}
