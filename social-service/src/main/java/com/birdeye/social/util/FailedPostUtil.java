package com.birdeye.social.util;

import com.birdeye.social.constant.SocialPostStatusEnum;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.service.FailedPostAuditService;
import com.birdeye.social.service.KafkaExternalService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Utility class for handling failed social post entries
 * This class provides methods to create and manage failed post entries in the social_post_publish_info table
 * 
 * <AUTHOR> Team
 */
@Component
public class FailedPostUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(FailedPostUtil.class);

    @Autowired
    private SocialPostInfoRepository socialPostInfoRepository;

    @Autowired
    private KafkaExternalService kafkaExternalService;

    @Autowired
    private FailedPostAuditService failedPostAuditService;

    /**
     * Creates a failed post entry with complete failure information
     * 
     * @param publishInfo The SocialPostPublishInfo object to mark as failed
     * @param failureReason The reason for the failure
     * @param failureCode The error code associated with the failure
     * @param bucket The bucket ID for error categorization
     */
    public void createFailedPostEntry(SocialPostPublishInfo publishInfo, String failureReason, Integer failureCode, Integer bucket) {
        try {
            LOGGER.info("Creating failed post entry for publishInfo ID: {} with reason: {}", publishInfo.getId(), failureReason);
            
            // Validate input parameters
            if (Objects.isNull(publishInfo)) {
                throw new IllegalArgumentException("PublishInfo cannot be null");
            }
            
            if (Objects.isNull(failureReason) || failureReason.trim().isEmpty()) {
                failureReason = "Unknown error occurred";
            }
            
            // Set the post as failed
            publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
            publishInfo.setFailureReason(failureReason);
            
            if (Objects.nonNull(failureCode)) {
                publishInfo.setFailureCode(failureCode);
            }
            
            if (Objects.nonNull(bucket)) {
                publishInfo.setBucket(bucket);
            }
            
            // Save to database
            socialPostInfoRepository.saveAndFlush(publishInfo);
            
            // Publish Kafka event for failed post
            kafkaExternalService.publishSocialPostEvent(publishInfo);
            
            // Create audit entry for failed post
            createFailedPostAudit(publishInfo, failureReason);
            
            LOGGER.info("Successfully created failed post entry for publishInfo ID: {}", publishInfo.getId());
            
        } catch (Exception e) {
            LOGGER.error("Error creating failed post entry for publishInfo ID: {}", 
                Objects.nonNull(publishInfo) ? publishInfo.getId() : "null", e);
            throw new RuntimeException("Failed to create failed post entry", e);
        }
    }

    /**
     * Creates a failed post entry with only failure reason
     * 
     * @param publishInfo The SocialPostPublishInfo object to mark as failed
     * @param failureReason The reason for the failure
     */
    public void createFailedPostEntry(SocialPostPublishInfo publishInfo, String failureReason) {
        createFailedPostEntry(publishInfo, failureReason, null, null);
    }

    /**
     * Creates a failed post entry for validation failures
     * 
     * @param publishInfo The SocialPostPublishInfo object to mark as failed
     * @param validationError The validation error message
     */
    public void createValidationFailedPostEntry(SocialPostPublishInfo publishInfo, String validationError) {
        String failureReason = "Validation failed: " + validationError;
        createFailedPostEntry(publishInfo, failureReason, 0, null);
    }

    /**
     * Creates a failed post entry for authentication failures
     * 
     * @param publishInfo The SocialPostPublishInfo object to mark as failed
     * @param authError The authentication error message
     */
    public void createAuthFailedPostEntry(SocialPostPublishInfo publishInfo, String authError) {
        String failureReason = "Authentication failed: " + authError;
        createFailedPostEntry(publishInfo, failureReason, 401, null);
    }

    /**
     * Creates a failed post entry for API failures
     * 
     * @param publishInfo The SocialPostPublishInfo object to mark as failed
     * @param apiError The API error message
     * @param errorCode The HTTP error code
     */
    public void createApiFailedPostEntry(SocialPostPublishInfo publishInfo, String apiError, Integer errorCode) {
        String failureReason = "API error: " + apiError;
        createFailedPostEntry(publishInfo, failureReason, errorCode, null);
    }

    /**
     * Creates an audit entry for failed post notification
     * 
     * @param publishInfo The failed post publish info
     * @param failureReason The reason for failure
     */
    private void createFailedPostAudit(SocialPostPublishInfo publishInfo, String failureReason) {
        try {
            LOGGER.info("Creating failed post audit for publishInfo ID: {} with failure reason: {}", publishInfo.getId(), failureReason);
            
            // Create audit entry using the FailedPostAuditService
            failedPostAuditService.saveAudit(
                publishInfo.getEnterpriseId(),
                publishInfo.getSocialPostId(),
                publishInfo.getExternalPageId(),
                null, // userEmailId - can be set if available
                failureReason, // payload
                "POST_FAILURE", // type
                "FAILED", // status
                "Post failed during publishing: " + failureReason // comment
            );
            
        } catch (Exception e) {
            LOGGER.error("Exception occurred while creating failed post audit for publishInfo ID: {}", publishInfo.getId(), e);
        }
    }

    /**
     * Checks if a post is already marked as failed
     * 
     * @param publishInfo The SocialPostPublishInfo object to check
     * @return true if the post is already failed, false otherwise
     */
    public boolean isPostAlreadyFailed(SocialPostPublishInfo publishInfo) {
        return Objects.nonNull(publishInfo) && 
               Objects.nonNull(publishInfo.getIsPublished()) && 
               publishInfo.getIsPublished().equals(SocialPostStatusEnum.FAILED.getId());
    }

    /**
     * Creates a failed post entry only if the post is not already failed
     * 
     * @param publishInfo The SocialPostPublishInfo object to mark as failed
     * @param failureReason The reason for the failure
     * @return true if the post was marked as failed, false if it was already failed
     */
    public boolean createFailedPostEntryIfNotFailed(SocialPostPublishInfo publishInfo, String failureReason) {
        if (isPostAlreadyFailed(publishInfo)) {
            LOGGER.info("Post with ID: {} is already marked as failed, skipping", publishInfo.getId());
            return false;
        }
        
        createFailedPostEntry(publishInfo, failureReason);
        return true;
    }
}
